const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function checkUsers() {
  console.log('Checking for test users...');
  
  const emails = ['<EMAIL>', '<EMAIL>'];
  
  for (const email of emails) {
    console.log(`\n--- Checking ${email} ---`);
    
    // Try to sign in to see what error we get
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: email,
      password: 'client123'
    });
    
    if (signInError) {
      console.log('Sign in error:', signInError.message);
      console.log('Error code:', signInError.status);
    } else {
      console.log('Sign in successful!');
      console.log('User ID:', signInData.user?.id);
    }
    
    // Check if user exists in auth.users using admin API
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();
    
    if (userError) {
      console.log('Error listing users:', userError.message);
    } else {
      const user = userData.users.find(u => u.email === email);
      if (user) {
        console.log('User found in auth.users:');
        console.log('- ID:', user.id);
        console.log('- Email confirmed:', user.email_confirmed_at ? 'Yes' : 'No');
        console.log('- Created:', user.created_at);
        console.log('- User metadata:', JSON.stringify(user.user_metadata, null, 2));
        console.log('- App metadata:', JSON.stringify(user.app_metadata, null, 2));
      } else {
        console.log('User NOT found in auth.users');
      }
    }
  }
}

checkUsers().catch(console.error);
