const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function debugTriggers() {
  console.log('Debugging <EMAIL> login issue...');
  
  const email = '<EMAIL>';
  const userId = '6bb1fbb5-c1e0-48bf-afc3-c967416b3e53';
  
  // Check if user exists in tenants.users table
  console.log('\n--- Checking tenants.users table ---');
  const { data: tenantUsers, error: tenantError } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId);
    
  if (tenantError) {
    console.log('Error querying tenants.users:', tenantError.message);
  } else {
    console.log('tenants.users record:', tenantUsers);
  }
  
  // Check tenant_id validity
  console.log('\n--- Checking tenant validity ---');
  const tenantId = 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11';
  const { data: tenant, error: firmError } = await supabase
    .from('firms')
    .select('*')
    .eq('tenant_id', tenantId);
    
  if (firmError) {
    console.log('Error querying firms:', firmError.message);
  } else {
    console.log('Tenant/firm record:', tenant);
  }
  
  // Try to manually create tenants.users record if missing
  if (!tenantUsers || tenantUsers.length === 0) {
    console.log('\n--- Creating missing tenants.users record ---');
    const { data: insertData, error: insertError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email: email,
        role: 'client',
        tenant_id: tenantId,
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString()
      });
      
    if (insertError) {
      console.log('Error creating tenants.users record:', insertError.message);
    } else {
      console.log('Successfully created tenants.users record');
    }
  }
  
  // Try login again
  console.log('\n--- Retrying login ---');
  const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
    email: email,
    password: 'client123'
  });
  
  if (retryError) {
    console.log('Login still fails:', retryError.message);
  } else {
    console.log('Login now works!');
  }
}

debugTriggers().catch(console.error);
