const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function recreateJohnDoe() {
  console.log('Recreating <EMAIL> with correct metadata...');
  
  const email = '<EMAIL>';
  const oldUserId = '6bb1fbb5-c1e0-48bf-afc3-c967416b3e53';
  const tenantId = 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11';
  const password = 'client123';
  
  try {
    // Step 1: Delete the existing user
    console.log('Deleting existing user...');
    const { error: deleteError } = await supabase.auth.admin.deleteUser(oldUserId);
    
    if (deleteError) {
      console.log('Delete error (might be expected):', deleteError.message);
    } else {
      console.log('Successfully deleted existing user');
    }
    
    // Step 2: Wait a moment for the deletion to propagate
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Create new user with correct metadata
    console.log('Creating new user with correct metadata...');
    const { data: createData, error: createError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        email_verified: true,
        role: 'client',
        tenant_id: tenantId,
        tenant_user_id: '7c9e6679-7425-40de-944b-e07fc1f90ae7'
      },
      app_metadata: {
        provider: 'email',
        providers: ['email'],
        role: 'client',
        tenant_id: tenantId
      }
    });
    
    if (createError) {
      console.log('Create error:', createError.message);
      console.log('Full error:', createError);
    } else {
      console.log('Successfully created new user');
      console.log('New user ID:', createData.user.id);
    }
    
    // Step 4: Test login
    console.log('\n--- Testing login ---');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: email,
      password: password
    });
    
    if (loginError) {
      console.log('Login fails:', loginError.message);
    } else {
      console.log('✅ Login works!');
      console.log('User ID:', loginData.user?.id);
      console.log('App metadata:', JSON.stringify(loginData.user?.app_metadata, null, 2));
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

recreateJohnDoe().catch(console.error);
