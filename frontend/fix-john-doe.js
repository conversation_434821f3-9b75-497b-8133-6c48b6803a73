const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function fixJohnDoe() {
  console.log('Fixing <EMAIL> login issue...');
  
  const email = '<EMAIL>';
  const userId = '6bb1fbb5-c1e0-48bf-afc3-c967416b3e53';
  const tenantId = 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11';
  
  try {
    // First, let's try to manually insert the user into tenants.users
    console.log('Creating tenants.users record...');
    
    // Use raw SQL to insert into tenants.users
    const { data: insertResult, error: insertError } = await supabase.rpc('exec_sql', {
      sql: `
        INSERT INTO tenants.users (id, email, role, tenant_id, created_at, last_login)
        VALUES (
          '${userId}',
          '${email}',
          'client',
          '${tenantId}',
          NOW(),
          NOW()
        )
        ON CONFLICT (id) DO UPDATE SET
          email = EXCLUDED.email,
          role = EXCLUDED.role,
          tenant_id = EXCLUDED.tenant_id,
          last_login = NOW()
        RETURNING *;
      `
    });
    
    if (insertError) {
      console.log('SQL insert error:', insertError.message);
      
      // Try alternative approach - update the auth user metadata to trigger the sync
      console.log('Trying to trigger sync by updating auth user metadata...');
      
      const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
        userId,
        {
          user_metadata: {
            email_verified: true,
            role: 'client',
            tenant_id: tenantId,
            tenant_user_id: '7c9e6679-7425-40de-944b-e07fc1f90ae7'
          }
        }
      );
      
      if (updateError) {
        console.log('Update metadata error:', updateError.message);
      } else {
        console.log('Successfully updated user metadata');
      }
    } else {
      console.log('Successfully created/updated tenants.users record');
    }
    
    // Now try login
    console.log('\n--- Testing login ---');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: email,
      password: 'client123'
    });
    
    if (loginError) {
      console.log('Login still fails:', loginError.message);
      console.log('Error details:', loginError);
    } else {
      console.log('✅ Login now works!');
      console.log('User ID:', loginData.user?.id);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

fixJohnDoe().catch(console.error);
