const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function fixAppMetadata() {
  console.log('Fixing <EMAIL> app_metadata...');
  
  const userId = '6bb1fbb5-c1e0-48bf-afc3-c967416b3e53';
  const tenantId = 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11';
  
  try {
    // Update the app_metadata (not user_metadata)
    console.log('Updating app_metadata...');
    
    const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
      userId,
      {
        app_metadata: {
          provider: 'email',
          providers: ['email'],
          role: 'client',
          tenant_id: tenantId
        }
      }
    );
    
    if (updateError) {
      console.log('Update app_metadata error:', updateError.message);
      console.log('Full error:', updateError);
    } else {
      console.log('Successfully updated app_metadata');
    }
    
    // Test login
    console.log('\n--- Testing login ---');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'client123'
    });
    
    if (loginError) {
      console.log('Login still fails:', loginError.message);
    } else {
      console.log('✅ Login now works!');
      console.log('User ID:', loginData.user?.id);
    }
    
    // Check the updated user
    console.log('\n--- Checking updated user ---');
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();
    
    if (!userError) {
      const user = userData.users.find(u => u.id === userId);
      if (user) {
        console.log('Updated user app_metadata:', JSON.stringify(user.app_metadata, null, 2));
      }
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

fixAppMetadata().catch(console.error);
